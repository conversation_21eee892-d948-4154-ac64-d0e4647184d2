const axios = require('axios');

class GeminiService {
  constructor() {
    this.apiKey = process.env.GEMINI_API_KEY || 'AIzaSyApLfuTDjZfegcpfzKqL37OpmL3WB3cALA';
    this.baseUrl = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent';
    this.model = 'gemini-2.0-flash';

    // Available dietary restrictions in the app (must match frontend options exactly)
    this.AVAILABLE_DIETARY_RESTRICTIONS = [
      'Vegetarian',
      'Vegan',
      'Gluten-Free',
      'Dairy-Free',
      'Nut-Free',
      'Low-Carb',
      'Keto',
      'Pescatarian',
      'Halal'
    ];

    // Available allergy options in the app (must match frontend options exactly)
    this.AVAILABLE_ALLERGIES = [
      'Nuts',
      'Gluten',
      'Soy',
      'Dairy',
      'Eggs',
      'Fish',
      'Shellfish',
      'Sesame'
    ];
  }

  async generateContent(prompt) {
    try {
      const response = await axios.post(`${this.baseUrl}?key=${this.apiKey}`, {
        contents: [
          {
            parts: [
              {
                text: prompt
              }
            ]
          }
        ],
        generationConfig: {
          temperature: 0.7,
          topK: 40,
          topP: 0.95,
          maxOutputTokens: 1024,
        }
      }, {
        headers: {
          'Content-Type': 'application/json'
        }
      });

      return response.data.candidates[0].content.parts[0].text;
    } catch (error) {
      console.error('Gemini API Error:', error.response?.data || error.message);
      throw new Error('Failed to generate AI response');
    }
  }

  // Detect conflicts in dietary preferences
  async detectDietaryConflicts(preferences) {
    const { restrictions, allergies, dislikedIngredients } = preferences;
    
    const prompt = `
    Analyze the following dietary preferences for conflicts and contradictions:
    
    Dietary Restrictions: ${restrictions?.join(', ') || 'None'}
    Allergies: ${allergies?.join(', ') || 'None'}
    Disliked Ingredients: ${dislikedIngredients?.join(', ') || 'None'}
    
    Please identify any contradictions or conflicts between these preferences. For example:
    - Keto + Vegan can be challenging due to limited protein sources
    - Dairy-free + Lactose intolerance is redundant
    - High-carb + Keto is contradictory
    
    Return your response in this JSON format:
    {
      "hasConflicts": true/false,
      "conflicts": [
        {
          "items": ["preference1", "preference2"],
          "reason": "explanation of the conflict",
          "severity": "high/medium/low"
        }
      ],
      "suggestions": ["suggestion1", "suggestion2"]
    }
    
    Only return the JSON, no additional text.
    `;

    try {
      const response = await this.generateContent(prompt);
      // Clean the response to extract JSON from markdown code blocks
      const cleanedResponse = this.extractJsonFromResponse(response);
      return JSON.parse(cleanedResponse);
    } catch (error) {
      console.error('Error detecting dietary conflicts:', error);
      return { hasConflicts: false, conflicts: [], suggestions: [] };
    }
  }

  // Generate meal recommendations based on family profile
  async generateMealRecommendations(familyProfile, availableMeals, goalType = null) {
    // Create a comprehensive profile summary
    const profileSummary = this.createProfileSummary(familyProfile);

    // Create a detailed meal list for the AI to choose from
    const mealList = availableMeals.map((meal, index) => `
    ${index + 1}. "${meal.name}"
       - Calories: ${meal.calories}, Protein: ${meal.protein}g, Carbs: ${meal.carbs}g, Fat: ${meal.fat}g
       - Categories: ${meal.category?.join(', ') || 'N/A'}
       - Meal Types: ${meal.mealType?.join(', ') || 'N/A'}
       - Dietary Tags: ${meal.dietaryTags?.join(', ') || 'N/A'}
       - Allergens: ${meal.allergens?.join(', ') || 'None'}
       - Vegetarian: ${meal.dietType?.isVegetarian ? 'Yes' : 'No'}
       - Vegan: ${meal.dietType?.isVegan ? 'Yes' : 'No'}
       - Gluten-Free: ${meal.dietType?.isGlutenFree ? 'Yes' : 'No'}
       - Dairy-Free: ${meal.dietType?.isDairyFree ? 'Yes' : 'No'}
       - Nut-Free: ${meal.dietType?.isNutFree ? 'Yes' : 'No'}
       - Low-Carb: ${meal.dietType?.isLowCarb ? 'Yes' : 'No'}
       - Keto: ${meal.dietType?.isKeto ? 'Yes' : 'No'}
       - Pescatarian: ${meal.dietType?.isPescatarian ? 'Yes' : 'No'}
       - Halal: ${meal.dietType?.isHalal ? 'Yes' : 'No'}
    `).join('');

    const prompt = `
    You are a nutritionist AI helping a Filipino family plan their meals. You MUST ONLY recommend meals from the exact list provided below.

    FAMILY PROFILE:
    ${profileSummary}

    ${goalType ? `CURRENT GOAL: ${goalType}` : ''}

    AVAILABLE MEALS IN DATABASE (you can ONLY choose from these):
    ${mealList}

    IMPORTANT RULES:
    1. You MUST ONLY recommend meals from the list above
    2. Use the EXACT meal names as written in quotes
    3. Recommend 5-8 meals maximum
    4. Consider the family's dietary restrictions, allergies, and goals
    5. Prioritize meals that match their preferences

    Return your response in this JSON format:
    {
      "recommendations": [
        {
          "mealName": "EXACT meal name from the list",
          "reason": "why this meal is recommended for this family",
          "nutritionalBenefits": "key nutritional benefits",
          "suitability": "how it fits the family profile"
        }
      ],
      "generalAdvice": "overall dietary advice for this family"
    }

    Only return the JSON, no additional text. Remember: ONLY use meal names that appear in the provided list.
    `;

    try {
      const response = await this.generateContent(prompt);
      const cleanedResponse = this.extractJsonFromResponse(response);
      const aiResponse = JSON.parse(cleanedResponse);

      // Validate that all recommended meals exist in the database
      const validatedRecommendations = aiResponse.recommendations.filter(rec => {
        const mealExists = availableMeals.some(meal =>
          meal.name.toLowerCase() === rec.mealName.toLowerCase()
        );
        if (!mealExists) {
          console.warn(`AI recommended non-existent meal: ${rec.mealName}`);
        }
        return mealExists;
      });

      return {
        recommendations: validatedRecommendations,
        generalAdvice: aiResponse.generalAdvice || ''
      };
    } catch (error) {
      console.error('Error generating meal recommendations:', error);
      return { recommendations: [], generalAdvice: '' };
    }
  }

  // Generate goal-based dietary suggestions
  async generateGoalBasedSuggestions(goal, healthCondition = null) {
    let prompt = `
    You are a nutritionist AI. A user has selected the goal: "${goal}".
    ${healthCondition ? `They also have the health condition: "${healthCondition}".` : ''}

    Based on this goal${healthCondition ? ' and health condition' : ''}, suggest appropriate dietary preferences from ONLY these available options in our app:

    Available Dietary Restrictions: ${this.AVAILABLE_DIETARY_RESTRICTIONS.join(', ')}

    Available Allergies to Avoid: ${this.AVAILABLE_ALLERGIES.join(', ')}

    IMPORTANT: You can ONLY recommend dietary restrictions and allergies from the lists above. Do not suggest any options that are not in these lists.

    Provide specific recommendations and explain why each suggestion is beneficial for their goal${healthCondition ? ' and condition' : ''}.

    Return your response in this JSON format:
    {
      "recommendedRestrictions": ["restriction1", "restriction2"],
      "recommendedAllergies": ["allergy1", "allergy2"],
      "explanation": "detailed explanation of why these are recommended",
      "additionalTips": ["tip1", "tip2", "tip3"]
    }

    Only return the JSON, no additional text. Remember: ONLY use options from the provided lists.
    `;

    try {
      const response = await this.generateContent(prompt);
      const cleanedResponse = this.extractJsonFromResponse(response);
      return JSON.parse(cleanedResponse);
    } catch (error) {
      console.error('Error generating goal-based suggestions:', error);
      return {
        recommendedRestrictions: [],
        recommendedAllergies: [],
        explanation: '',
        additionalTips: []
      };
    }
  }

  // Create a comprehensive profile summary
  createProfileSummary(familyProfile) {
    const { dietaryPreferences, familyMembers } = familyProfile;
    
    let summary = `
    DIETARY PREFERENCES:
    - Restrictions: ${dietaryPreferences?.restrictions?.join(', ') || 'None'}
    - Allergies: ${dietaryPreferences?.allergies?.join(', ') || 'None'}
    - Disliked Ingredients: ${dietaryPreferences?.dislikedIngredients?.join(', ') || 'None'}
    - Daily Calorie Target: ${dietaryPreferences?.calorieTarget || 'Not specified'}
    - Meals Per Day: ${dietaryPreferences?.mealFrequency || 3}
    `;

    if (familyMembers && familyMembers.length > 0) {
      summary += `\n    FAMILY MEMBERS:`;
      familyMembers.forEach((member, index) => {
        const age = member.dateOfBirth ? this.calculateAge(member.dateOfBirth) : 'Unknown';
        summary += `\n    ${index + 1}. ${member.name} (Age: ${age})`;
        if (member.dietaryPreferences) {
          summary += `\n       - Restrictions: ${member.dietaryPreferences.restrictions?.join(', ') || 'None'}`;
          summary += `\n       - Allergies: ${member.dietaryPreferences.allergies?.join(', ') || 'None'}`;
          summary += `\n       - Calorie Target: ${member.dietaryPreferences.calorieTarget || 'Not specified'}`;
        }
      });
    }

    return summary;
  }

  // Helper function to calculate age
  calculateAge(dateOfBirth) {
    const today = new Date();
    const birthDate = new Date(dateOfBirth);
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();

    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }

    return age;
  }

  // Helper function to extract JSON from markdown code blocks
  extractJsonFromResponse(response) {
    // Remove markdown code block markers
    let cleaned = response.replace(/```json\s*/g, '').replace(/```\s*/g, '');

    // Try to find JSON object boundaries
    const jsonStart = cleaned.indexOf('{');
    const jsonEnd = cleaned.lastIndexOf('}');

    if (jsonStart !== -1 && jsonEnd !== -1 && jsonEnd > jsonStart) {
      cleaned = cleaned.substring(jsonStart, jsonEnd + 1);
    }

    return cleaned.trim();
  }

  // Generate chat response for general queries
  async generateChatResponse(message, context = {}) {
    let availableMealsContext = '';

    // If context includes available meals, add them to the prompt
    if (context.availableMeals && context.availableMeals.length > 0) {
      const mealNames = context.availableMeals.map(meal => meal.name).join(', ');
      availableMealsContext = `\n\nAvailable meals in our database: ${mealNames}`;
    }

    const prompt = `
    You are a helpful Filipino meal planning assistant. The user has sent you this message: "${message}"

    ${context.familyProfile ? `User's Family Profile: ${this.createProfileSummary(context.familyProfile)}` : ''}
    ${availableMealsContext}

    Provide a helpful, friendly response related to meal planning, nutrition, or Filipino cuisine. Keep your response concise and actionable.

    IMPORTANT: If the user asks about meal recommendations or specific dishes, ONLY mention meals that are available in our database (listed above).

    If the user is asking about meal recommendations, dietary advice, or nutrition, provide specific suggestions from our available meals.
    If the user is asking about Filipino dishes, provide cultural context and preparation tips, but focus on dishes we have available.

    Return your response as plain text, maximum 200 words.
    `;

    try {
      const response = await this.generateContent(prompt);
      return response;
    } catch (error) {
      console.error('Error generating chat response:', error);
      return "I'm sorry, I'm having trouble responding right now. Please try again later.";
    }
  }
}

module.exports = new GeminiService();
