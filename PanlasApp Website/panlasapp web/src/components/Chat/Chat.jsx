import React, { useState, useEffect, useRef } from "react";
import Layout from "../Layout/Layout";
import aiAPI from "../../services/aiAPI";
import "./Chat.css";

const Chat = () => {
  const [messages, setMessages] = useState([]);
  const [inputText, setInputText] = useState('');
  const [loading, setLoading] = useState(false);
  const [selectedGoal, setSelectedGoal] = useState(null);
  const [selectedHealthCondition, setSelectedHealthCondition] = useState(null);
  const [showGoalSelection, setShowGoalSelection] = useState(true);
  const [showChatInput, setShowChatInput] = useState(false);
  const [goals, setGoals] = useState([]);
  const [healthConditions, setHealthConditions] = useState([]);

  const messagesEndRef = useRef(null);

  const defaultGoals = [
    { id: 'lose_weight', name: 'Lose Weight', description: 'Get meal suggestions to help with weight loss' },
    { id: 'build_muscle', name: 'Build Muscle', description: 'Get high-protein meals to support muscle building' },
    { id: 'manage_health', name: 'Manage a Health Condition', description: 'Get meals tailored to specific health conditions' },
    { id: 'eat_sustainably', name: 'Eat Sustainably', description: 'Get environmentally conscious meal suggestions' },
    { id: 'other', name: 'Other', description: 'Chat directly with AI for custom dietary advice' }
  ];

  const defaultHealthConditions = [
    { id: 'type2_diabetes', name: 'Type 2 Diabetes', description: 'Low-sugar, low-carb meal recommendations' },
    { id: 'celiac_disease', name: 'Celiac Disease', description: 'Gluten-free meal recommendations' },
    { id: 'hypertension', name: 'Hypertension', description: 'Low-sodium meal recommendations' },
    { id: 'heart_disease', name: 'Heart Disease', description: 'Heart-healthy, low-cholesterol meals' },
    { id: 'lactose_intolerance', name: 'Lactose Intolerance', description: 'Dairy-free meal recommendations' }
  ];

  useEffect(() => {
    loadGoalsAndConditions();
    addWelcomeMessage();
  }, []);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  const loadGoalsAndConditions = async () => {
    try {
      const response = await aiAPI.getGoalsAndConditions();
      if (response.success) {
        setGoals(response.goals || defaultGoals);
        setHealthConditions(response.healthConditions || defaultHealthConditions);
      } else {
        setGoals(defaultGoals);
        setHealthConditions(defaultHealthConditions);
      }
    } catch (error) {
      console.error('Error loading goals:', error);
      setGoals(defaultGoals);
      setHealthConditions(defaultHealthConditions);
    }
  };

  const addWelcomeMessage = () => {
    const welcomeMessage = {
      id: Date.now(),
      text: "Hello! I'm your AI meal planning assistant. To get started, please select one of your health goals below, and I'll provide personalized dietary recommendations for you.",
      isUser: false,
      timestamp: new Date(),
    };
    setMessages([welcomeMessage]);
  };

  const selectGoal = async (goal) => {
    setSelectedGoal(goal);
    setShowGoalSelection(false);

    const goalMessage = {
      id: Date.now(),
      text: goal.id === 'other' ? 'I want to chat about something else' : `I want to ${goal.name.toLowerCase()}`,
      isUser: true,
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, goalMessage]);

    if (goal.id === 'other') {
      // For "Other", enable free chat mode permanently
      const aiMessage = {
        id: Date.now() + 1,
        text: "I'm here to help with any dietary questions or meal planning needs you have! Please tell me what you'd like to know about nutrition, meals, or healthy eating.",
        isUser: false,
        timestamp: new Date(),
      };
      setMessages(prev => [...prev, aiMessage]);
      setShowChatInput(true);
    } else if (goal.id === 'manage_health') {
      // Show health condition selection
      const responseMessage = {
        id: Date.now() + 1,
        text: "Great! Please select the specific health condition you'd like to manage:",
        isUser: false,
        timestamp: new Date(),
        showHealthConditions: true,
      };

      setMessages(prev => [...prev, responseMessage]);
    } else {
      // Get AI suggestions for other goals
      await getGoalSuggestions(goal);
    }
  };

  const selectHealthCondition = async (condition) => {
    setSelectedHealthCondition(condition);

    const conditionMessage = {
      id: Date.now(),
      text: `I want to manage ${condition.name}`,
      isUser: true,
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, conditionMessage]);

    // Get AI suggestions for the selected health condition
    await getGoalSuggestions(selectedGoal, condition);
  };

  const getGoalSuggestions = async (goal, healthCondition = null) => {
    try {
      setLoading(true);

      const response = await aiAPI.getGoalSuggestions({
        goal: goal.name,
        healthCondition: healthCondition?.name
      });

      if (response.success && response.suggestions) {
        const suggestions = response.suggestions;
        let responseText = suggestions.explanation;

        if (suggestions.recommendedRestrictions.length > 0) {
          responseText += `\n\n🥗 Recommended Dietary Restrictions:\n${suggestions.recommendedRestrictions.map(r => `• ${r}`).join('\n')}`;
        }

        if (suggestions.recommendedAllergies.length > 0) {
          responseText += `\n\n⚠️ Consider avoiding:\n${suggestions.recommendedAllergies.map(a => `• ${a}`).join('\n')}`;
        }

        if (suggestions.additionalTips.length > 0) {
          responseText += `\n\n💡 Additional Tips:\n${suggestions.additionalTips.map(tip => `• ${tip}`).join('\n')}`;
        }

        responseText += `\n\nWould you like me to recommend specific meals from our database that match these preferences?`;

        const aiMessage = {
          id: Date.now(),
          text: responseText,
          isUser: false,
          timestamp: new Date(),
        };

        setMessages(prev => [...prev, aiMessage]);

        // Enable chat input after AI responds so user can continue conversation
        setShowChatInput(true);
      }
    } catch (error) {
      console.error('Error getting goal suggestions:', error);
      const errorMessage = {
        id: Date.now(),
        text: "I'm sorry, I'm having trouble providing suggestions right now. Please try again later.",
        isUser: false,
        timestamp: new Date(),
      };
      setMessages(prev => [...prev, errorMessage]);

      // Enable chat input even on error so user can try again
      setShowChatInput(true);
    } finally {
      setLoading(false);
    }
  };

  const sendMessage = async (e) => {
    e.preventDefault();
    if (!inputText.trim()) return;

    const userMessage = {
      id: Date.now(),
      text: inputText,
      isUser: true,
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, userMessage]);
    setInputText('');
    setLoading(true);

    try {
      const response = await aiAPI.sendChatMessage({
        message: inputText,
        includeProfile: true,
        includeMeals: true
      });

      if (response.success) {
        const aiMessage = {
          id: Date.now() + 1,
          text: response.response,
          isUser: false,
          timestamp: new Date(),
        };
        setMessages(prev => [...prev, aiMessage]);
      }
    } catch (error) {
      console.error('Error sending message:', error);
      const errorMessage = {
        id: Date.now() + 1,
        text: "I'm sorry, I'm having trouble responding right now. Please try again later.",
        isUser: false,
        timestamp: new Date(),
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setLoading(false);
    }
  };

  const resetChat = () => {
    setMessages([]);
    setSelectedGoal(null);
    setSelectedHealthCondition(null);
    setShowGoalSelection(true);
    setShowChatInput(false);
    addWelcomeMessage();
  };

  return (
    <Layout>
      <div className="main-content">
        <div className="chat-container">
          <div className="chat-header">
            <h1>AI Meal Assistant</h1>
            <button onClick={resetChat} className="reset-chat-btn">
              🔄 Reset Chat
            </button>
          </div>

          <div className="chat-messages">
            {messages.map((message) => (
              <div key={message.id} className={`message ${message.isUser ? 'user-message' : 'ai-message'}`}>
                <div className="message-content">
                  <div className="message-text">
                    {message.text.split('\n').map((line, index) => (
                      <div key={index}>{line}</div>
                    ))}
                  </div>
                  <div className="message-timestamp">
                    {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                  </div>
                </div>

                {message.showHealthConditions && (
                  <div className="health-conditions-grid">
                    {healthConditions.map(condition => (
                      <button
                        key={condition.id}
                        className="health-condition-btn"
                        onClick={() => selectHealthCondition(condition)}
                      >
                        <div className="condition-name">{condition.name}</div>
                        <div className="condition-description">{condition.description}</div>
                      </button>
                    ))}
                  </div>
                )}
              </div>
            ))}

            {/* Goal Selection */}
            {showGoalSelection && messages.length > 0 && (
              <div className="goals-selection">
                <h3>Select your goal:</h3>
                <div className="goals-grid">
                  {goals.map(goal => (
                    <button
                      key={goal.id}
                      className="goal-btn"
                      onClick={() => selectGoal(goal)}
                    >
                      <div className="goal-name">{goal.name}</div>
                      <div className="goal-description">{goal.description}</div>
                    </button>
                  ))}
                </div>
              </div>
            )}

            {loading && (
              <div className="loading-message">
                <div className="typing-indicator">
                  <span></span>
                  <span></span>
                  <span></span>
                </div>
                <span>AI is thinking...</span>
              </div>
            )}

            <div ref={messagesEndRef} />
          </div>

          {/* Chat Input - Only show when enabled */}
          {showChatInput && (
            <form onSubmit={sendMessage} className="chat-input-form">
              <div className="chat-input-container">
                <input
                  type="text"
                  value={inputText}
                  onChange={(e) => setInputText(e.target.value)}
                  placeholder="Ask me about meals, nutrition, or dietary advice..."
                  className="chat-input"
                  disabled={loading}
                  maxLength={500}
                />
                <button
                  type="submit"
                  disabled={!inputText.trim() || loading}
                  className="send-btn"
                >
                  Send
                </button>
              </div>
            </form>
          )}
        </div>
      </div>
    </Layout>
  );
};

export default Chat;
  