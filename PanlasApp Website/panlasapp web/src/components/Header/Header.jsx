import React, { useState, useEffect, useRef } from "react";
import { Link, useNavigate, useLocation } from "react-router-dom";
import "../../../src/App.css";
import axios from "axios";
import { useAdminView } from "../../context/AdminViewContext";
import analyticsService from "../../services/analyticsService";
// images
import logo from "../../assets/PanlasApp.png";
import profile from "../../assets/user.png";
import arrow from "../../assets/dropdownsign.png";

const Header = ({
  toggleSidebar,
  openMealDetails,
  searchTerm,
  setSearchTerm,
}) => {
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [isAdmin, setIsAdmin] = useState(false);
  const [userName, setUserName] = useState("User");
  const dropdownRef = useRef(null);
  const searchRef = useRef(null);
  const { isViewingAsUser, toggleViewMode } = useAdminView();
  const navigate = useNavigate();
  const location = useLocation();

  // User profile logic (unchanged)
  useEffect(() => {
    const fetchUserProfile = async () => {
      const token = localStorage.getItem("token");
      const userInfo = localStorage.getItem("user");
      if (!token) return;
      if (userInfo) {
        try {
          const parsedUser = JSON.parse(userInfo);
          setUserName(parsedUser.username || "User");
        } catch (error) {
          console.error("Error parsing user info:", error);
        }
      }
      try {
        const response = await axios.get(
          "http://localhost:5000/api/users/profile",
          {
            headers: {
              Authorization: `Bearer ${token}`,
            },
          }
        );
        setIsAdmin(response.data.isAdmin || false);
        if (response.data.username) {
          setUserName(response.data.username);
          const updatedUserInfo = {
            id: response.data._id,
            email: response.data.email,
            username: response.data.username,
          };
          localStorage.setItem("user", JSON.stringify(updatedUserInfo));
        }
      } catch (error) {
        console.error("Error fetching user profile:", error);
      }
    };
    fetchUserProfile();
  }, []);

  const toggleDropdown = () => {
    setIsDropdownOpen(!isDropdownOpen);
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsDropdownOpen(false);
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const handleLogout = async () => {
    try {
      // Track logout analytics before clearing token
      await analyticsService.trackEvent('logout', {
        logoutMethod: 'manual',
        page: window.location.pathname
      });

      // Call backend logout endpoint
      const token = localStorage.getItem("token");
      if (token) {
        try {
          await axios.post('http://localhost:5000/api/users/logout', {}, {
            headers: {
              'Authorization': `Bearer ${token}`,
              'x-session-id': sessionStorage.getItem('sessionId')
            }
          });
        } catch (error) {
          console.error('Logout API call failed:', error);
        }
      }

      // Clear local storage
      localStorage.removeItem("token");
      localStorage.removeItem("user");
      sessionStorage.removeItem("sessionId");

      // Disable analytics tracking
      analyticsService.disable();

      // Redirect to landing page
      window.location.href = "/";
    } catch (error) {
      console.error('Logout error:', error);
      // Still proceed with logout even if analytics fails
      localStorage.removeItem("token");
      localStorage.removeItem("user");
      sessionStorage.removeItem("sessionId");

      // Disable analytics tracking
      analyticsService.disable();

      window.location.href = "/";
    }
  };

  const handleViewToggle = () => {
    // If currently on an admin page and switching to user view, redirect to home
    const adminPaths = ['/admin', '/feedback-management'];
    const isOnAdminPage = adminPaths.includes(location.pathname);

    if (!isViewingAsUser && isOnAdminPage) {
      // Switching from admin view to user view while on admin page
      navigate('/home');
    }

    toggleViewMode();
  };

  const handleSearchChange = (e) => {
    setSearchTerm(e.target.value);
  };

  return (
    <header className="header">
      <div className="container">
        <div className="header-content">
          <div className="left">
            <div className="mobile-menu-toggle" onClick={toggleSidebar}>
              <span></span>
              <span></span>
              <span></span>
            </div>
            <Link to="/home" className="logo-container">
              <div className="logo">
                <img src={logo} alt="PanlasApp Logo" />
              </div>
              <span className="logo-text-home">PanlasApp</span>
            </Link>
          </div>
          <div className="search" ref={searchRef}>
            <input
              type="text"
              placeholder="Search..."
              value={searchTerm}
              onChange={handleSearchChange}
            />
          </div>
          <div className="right">
            {/* Admin View Toggle Button */}
            {isAdmin && (
              <button
                className={`admin-view-toggle ${isViewingAsUser ? 'user-mode' : 'admin-mode'}`}
                onClick={handleViewToggle}
                title={isViewingAsUser ? 'Back to Admin View' : 'View as User'}
              >
                {isViewingAsUser ? 'Back to Admin' : 'View as User'}
              </button>
            )}
            <div className="profile-dropdown" ref={dropdownRef}>
              <div className="profile-wrapper" onClick={toggleDropdown}>
                <img src={profile} className="profile-pic" alt="User Profile" />
                <div className="profile-info">
                  <span className="profile-name">{userName}</span>
                  <img
                    src={arrow}
                    className={`dropdown-arrow ${isDropdownOpen ? "rotated" : ""}`}
                    alt="Dropdown"
                  />
                </div>
              </div>
              {isDropdownOpen && (
                <div className="dropdown-menu">
                  <Link to="/profile" className="dropdown-item">
                    My Profile
                  </Link>
                  {isAdmin && !isViewingAsUser && (
                    <Link to="/admin" className="dropdown-item admin-link">
                      Admin Dashboard
                    </Link>
                  )}
                  {isAdmin && !isViewingAsUser && (
                    <Link to="/feedback-management" className="dropdown-item admin-link">
                      Feedbacks
                    </Link>
                  )}
                  <div className="dropdown-divider"></div>
                  <button
                    onClick={handleLogout}
                    className="dropdown-item logout"
                  >
                    Logout
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </header>
  );
};

export default Header;
