import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import userAPI from '../../services/userAPI';
import aiAPI from '../../services/aiAPI';
import Header from '../Header/Header';
import Sidebar from '../Sidebar/Sidebar';
import './DietaryPreferences.css';

const DietaryPreferences = () => {
  const [preferences, setPreferences] = useState({
    restrictions: [],
    allergies: [],
    dislikedIngredients: [],
    calorieTarget: '',
    mealFrequency: 3
  });
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [sidebarActive, setSidebarActive] = useState(false);
  const [conflicts, setConflicts] = useState(null);
  const [checkingConflicts, setCheckingConflicts] = useState(false);
  const [familyMembers, setFamilyMembers] = useState([]);
  const navigate = useNavigate();

  // Dietary restrictions options (matching mobile app and backend)
  const dietaryOptions = [
    'Vegetarian',
    'Vegan',
    'Gluten-Free',
    'Dairy-Free',
    'Nut-Free',
    'Low-Carb',
    'Keto',
    'Pescatarian',
    'Halal'
  ];

  // Allergy options (matching mobile app and backend)
  const allergyOptions = [
    'Nuts',
    'Gluten',
    'Soy',
    'Dairy',
    'Eggs',
    'Fish',
    'Shellfish',
    'Sesame'
  ];

  useEffect(() => {
    loadPreferences();
    loadFamilyMembers();
  }, []);

  const loadFamilyMembers = async () => {
    try {
      const response = await userAPI.getFamilyMembers();
      setFamilyMembers(response.data || []);
    } catch (error) {
      console.error('Error loading family members:', error);
      setFamilyMembers([]);
    }
  };

  const loadPreferences = async () => {
    try {
      setLoading(true);
      setError('');
      const response = await userAPI.getDietaryPreferences();
      console.log('Loaded preferences:', response);
      
      if (response.success && response.dietaryPreferences) {
        setPreferences({
          restrictions: response.dietaryPreferences.restrictions || [],
          allergies: response.dietaryPreferences.allergies || [],
          dislikedIngredients: response.dietaryPreferences.dislikedIngredients || [],
          calorieTarget: response.dietaryPreferences.calorieTarget?.toString() || '',
          mealFrequency: response.dietaryPreferences.mealFrequency || 3
        });
      }
    } catch (error) {
      console.error('Error loading preferences:', error);
      setError('Failed to load dietary preferences');
    } finally {
      setLoading(false);
    }
  };

  const savePreferences = async () => {
    try {
      setSaving(true);
      setError('');
      setSuccess('');

      const preferencesToSave = {
        restrictions: preferences.restrictions,
        allergies: preferences.allergies,
        dislikedIngredients: preferences.dislikedIngredients,
        calorieTarget: preferences.calorieTarget ? parseInt(preferences.calorieTarget) : null,
        mealFrequency: preferences.mealFrequency
      };

      console.log('Saving preferences:', preferencesToSave);

      const response = await userAPI.updateDietaryPreferences(preferencesToSave);
      console.log('Save response:', response);

      if (response.success) {
        setSuccess('Dietary preferences saved successfully!');
        setTimeout(() => setSuccess(''), 3000);
      } else {
        setError(response.error || 'Failed to save preferences');
      }
    } catch (error) {
      console.error('Error saving preferences:', error);
      setError('Failed to save preferences');
    } finally {
      setSaving(false);
    }
  };

  const toggleRestriction = (restriction) => {
    setPreferences(prev => ({
      ...prev,
      restrictions: prev.restrictions.includes(restriction)
        ? prev.restrictions.filter(r => r !== restriction)
        : [...prev.restrictions, restriction]
    }));
  };

  const toggleAllergy = (allergy) => {
    setPreferences(prev => ({
      ...prev,
      allergies: prev.allergies.includes(allergy)
        ? prev.allergies.filter(a => a !== allergy)
        : [...prev.allergies, allergy]
    }));
  };

  const addDislikedIngredient = (ingredient) => {
    if (ingredient.trim() && !preferences.dislikedIngredients.includes(ingredient.trim())) {
      setPreferences(prev => ({
        ...prev,
        dislikedIngredients: [...prev.dislikedIngredients, ingredient.trim()]
      }));
    }
  };

  const removeDislikedIngredient = (ingredient) => {
    setPreferences(prev => ({
      ...prev,
      dislikedIngredients: prev.dislikedIngredients.filter(i => i !== ingredient)
    }));
  };

  const handleDislikedIngredientKeyPress = (e) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      addDislikedIngredient(e.target.value);
      e.target.value = '';
    }
  };

  const toggleSidebar = () => {
    setSidebarActive(!sidebarActive);
  };

  // Check for dietary conflicts using AI
  const checkDietaryConflicts = async (currentPreferences) => {
    try {
      setCheckingConflicts(true);

      // Prepare family member preferences for conflict checking
      const familyPreferences = familyMembers.map(member => ({
        name: member.name,
        restrictions: member.dietaryPreferences?.restrictions || [],
        allergies: member.dietaryPreferences?.allergies || [],
        dislikedIngredients: member.dietaryPreferences?.dislikedIngredients || []
      }));

      const response = await aiAPI.detectDietaryConflicts({
        userPreferences: {
          restrictions: currentPreferences.restrictions,
          allergies: currentPreferences.allergies,
          dislikedIngredients: currentPreferences.dislikedIngredients
        },
        familyMembers: familyPreferences
      });

      if (response.success && response.conflicts) {
        setConflicts(response.conflicts);
      }
    } catch (error) {
      console.error('Error checking dietary conflicts:', error);
      // Don't show error to user, just log it
    } finally {
      setCheckingConflicts(false);
    }
  };

  // Check for conflicts whenever preferences change
  useEffect(() => {
    if (preferences.restrictions.length > 0 || preferences.allergies.length > 0) {
      const timeoutId = setTimeout(() => {
        checkDietaryConflicts(preferences);
      }, 1000); // Debounce for 1 second

      return () => clearTimeout(timeoutId);
    } else {
      setConflicts(null);
    }
  }, [preferences.restrictions, preferences.allergies, preferences.dislikedIngredients]);

  if (loading) {
    return (
      <div className="app-container">
        <Header toggleSidebar={toggleSidebar} />
        <Sidebar isActive={sidebarActive} />
        <div className="content-area">
          <div className="main-content">
            <div className="loading-container">
              <div className="loading-spinner"></div>
              <p>Loading dietary preferences...</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="app-container">
      <Header toggleSidebar={toggleSidebar} />
      <Sidebar isActive={sidebarActive} />
      <div className="content-area">
        <div className="main-content">
          <div className="dietary-preferences-container">
            <div className="dietary-preferences-header">
              <button 
                className="back-button"
                onClick={() => navigate('/profile')}
              >
                ← Back to Profile
              </button>
              <h1>Dietary Preferences</h1>
              <p>Set your dietary restrictions, allergies, and preferences to get personalized meal recommendations.</p>
            </div>

            {error && <div className="error-message">{error}</div>}
            {success && <div className="success-message">{success}</div>}

            <div className="preferences-form">
              {/* Dietary Restrictions */}
              <div className="preference-section">
                <h3>Dietary Restrictions</h3>
                <p>Select any dietary restrictions you follow:</p>
                <div className="options-grid">
                  {dietaryOptions.map((option) => (
                    <label key={option} className="option-checkbox">
                      <input
                        type="checkbox"
                        checked={preferences.restrictions.includes(option)}
                        onChange={() => toggleRestriction(option)}
                      />
                      <span className="checkmark"></span>
                      {option}
                    </label>
                  ))}
                </div>
              </div>

              {/* Allergies */}
              <div className="preference-section">
                <h3>Allergies</h3>
                <p>Select any food allergies you have:</p>
                <div className="options-grid">
                  {allergyOptions.map((option) => (
                    <label key={option} className="option-checkbox">
                      <input
                        type="checkbox"
                        checked={preferences.allergies.includes(option)}
                        onChange={() => toggleAllergy(option)}
                      />
                      <span className="checkmark"></span>
                      {option}
                    </label>
                  ))}
                </div>
              </div>

              {/* AI Conflict Detection Warning */}
              {conflicts && conflicts.hasConflicts && (
                <div className="conflict-warning-section">
                  <div className="conflict-header">
                    <span className="warning-icon">⚠️</span>
                    <h3>Dietary Conflicts Detected</h3>
                  </div>
                  {conflicts.conflicts.map((conflict, index) => (
                    <div key={index} className="conflict-item">
                      <div className="conflict-text">
                        <strong className="conflict-items">
                          {conflict.items.join(' + ')}
                        </strong>
                        {conflict.type === 'family' && (
                          <span className="conflict-type"> (Family Conflict)</span>
                        )}
                        {conflict.type === 'user-family' && (
                          <span className="conflict-type"> (User vs Family)</span>
                        )}
                        : {conflict.reason}
                        {conflict.severity === 'high' && (
                          <span className="severity-badge high">High Priority</span>
                        )}
                      </div>
                    </div>
                  ))}
                  {conflicts.suggestions && conflicts.suggestions.length > 0 && (
                    <div className="suggestions-container">
                      <h4>AI Suggestions:</h4>
                      <ul className="suggestions-list">
                        {conflicts.suggestions.map((suggestion, index) => (
                          <li key={index}>{suggestion}</li>
                        ))}
                      </ul>
                    </div>
                  )}
                  <p className="conflict-note">
                    Note: You can still save these preferences, but consider the conflicts above.
                  </p>
                </div>
              )}

              {/* Loading indicator for conflict checking */}
              {checkingConflicts && (
                <div className="checking-conflicts">
                  <div className="spinner"></div>
                  <span>Checking for dietary conflicts...</span>
                </div>
              )}

              {/* Disliked Ingredients */}
              <div className="preference-section">
                <h3>Disliked Ingredients</h3>
                <p>Add ingredients you prefer to avoid:</p>
                <div className="disliked-ingredients">
                  <input
                    type="text"
                    placeholder="Type an ingredient and press Enter"
                    onKeyPress={handleDislikedIngredientKeyPress}
                    className="ingredient-input"
                  />
                  <div className="ingredients-list">
                    {preferences.dislikedIngredients.map((ingredient, index) => (
                      <span key={index} className="ingredient-tag">
                        {ingredient}
                        <button
                          type="button"
                          onClick={() => removeDislikedIngredient(ingredient)}
                          className="remove-ingredient"
                        >
                          ×
                        </button>
                      </span>
                    ))}
                  </div>
                </div>
              </div>

              {/* Calorie Target */}
              <div className="preference-section">
                <h3>Daily Calorie Target</h3>
                <p>Set your daily calorie goal (optional):</p>
                <input
                  type="number"
                  placeholder="e.g., 2000"
                  value={preferences.calorieTarget}
                  onChange={(e) => setPreferences(prev => ({
                    ...prev,
                    calorieTarget: e.target.value
                  }))}
                  className="calorie-input"
                  min="1000"
                  max="5000"
                />
              </div>

              {/* Meal Frequency */}
              <div className="preference-section">
                <h3>Meal Frequency</h3>
                <p>How many meals do you prefer per day?</p>
                <div className="frequency-container">
                  {[2, 3, 4, 5, 6].map(frequency => (
                    <button
                      key={frequency}
                      className={`frequency-button ${preferences.mealFrequency === frequency ? 'frequency-button-selected' : ''}`}
                      onClick={() => setPreferences(prev => ({ ...prev, mealFrequency: frequency }))}
                      type="button"
                    >
                      {frequency}
                    </button>
                  ))}
                </div>
              </div>

              {/* Summary Section */}
              <div className="preference-section summary-section">
                <h3>Summary</h3>
                <div className="summary-card">
                  <div className="summary-row">
                    <span className="summary-label">Restrictions:</span>
                    <span className="summary-value">
                      {preferences.restrictions.length > 0 ? preferences.restrictions.join(', ') : 'None'}
                    </span>
                  </div>
                  <div className="summary-row">
                    <span className="summary-label">Allergies:</span>
                    <span className="summary-value">
                      {preferences.allergies.length > 0 ? preferences.allergies.join(', ') : 'None'}
                    </span>
                  </div>
                  <div className="summary-row">
                    <span className="summary-label">Daily Calories:</span>
                    <span className="summary-value">
                      {preferences.calorieTarget || 'Not set'}
                    </span>
                  </div>
                  <div className="summary-row">
                    <span className="summary-label">Meals per day:</span>
                    <span className="summary-value">
                      {preferences.mealFrequency}
                    </span>
                  </div>
                </div>
              </div>

              {/* Save Button */}
              <div className="save-section">
                <button
                  onClick={savePreferences}
                  disabled={saving}
                  className="save-preferences-btn"
                >
                  {saving ? 'Saving...' : 'Save Preferences'}
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DietaryPreferences;
