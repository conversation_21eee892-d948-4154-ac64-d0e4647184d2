/* Dietary Preferences Styles */
.dietary-preferences-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.dietary-preferences-header {
  margin-bottom: 30px;
  text-align: center;
}

.back-button {
  background: none;
  border: none;
  color: #70e4c4;
  font-size: 16px;
  cursor: pointer;
  margin-bottom: 20px;
  padding: 8px 16px;
  border-radius: 6px;
  transition: background-color 0.3s;
  align-self: flex-start;
}

.back-button:hover {
  background-color: rgba(112, 228, 196, 0.1);
}

.dietary-preferences-header h1 {
  color: #2c3e50;
  margin-bottom: 10px;
  font-size: 32px;
  font-weight: 700;
}

.dietary-preferences-header p {
  color: #78909c;
  font-size: 16px;
  line-height: 1.5;
}

.preferences-form {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.preference-section {
  background: #f8f9fa;
  padding: 24px;
  border-radius: 12px;
  border: 1px solid #e9ecef;
}

.preference-section h3 {
  color: #2c3e50;
  margin-bottom: 8px;
  font-size: 20px;
  font-weight: 600;
}

.preference-section p {
  color: #78909c;
  margin-bottom: 16px;
  font-size: 14px;
}

.options-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
}

.option-checkbox {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 12px 16px;
  background: white;
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  transition: all 0.3s;
  font-size: 14px;
  font-weight: 500;
}

.option-checkbox:hover {
  border-color: #70e4c4;
  background-color: rgba(112, 228, 196, 0.05);
}

.option-checkbox input[type="checkbox"] {
  display: none;
}

.checkmark {
  width: 20px;
  height: 20px;
  border: 2px solid #e0e0e0;
  border-radius: 4px;
  margin-right: 12px;
  position: relative;
  transition: all 0.3s;
}

.option-checkbox input[type="checkbox"]:checked + .checkmark {
  background-color: #70e4c4;
  border-color: #70e4c4;
}

.option-checkbox input[type="checkbox"]:checked + .checkmark::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 12px;
  font-weight: bold;
}

.option-checkbox input[type="checkbox"]:checked {
  background-color: #70e4c4;
  border-color: #70e4c4;
}

.disliked-ingredients {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.ingredient-input {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  font-size: 14px;
  transition: border-color 0.3s;
}

.ingredient-input:focus {
  outline: none;
  border-color: #70e4c4;
  box-shadow: 0 0 0 3px rgba(112, 228, 196, 0.2);
}

.ingredients-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.ingredient-tag {
  display: inline-flex;
  align-items: center;
  background-color: #70e4c4;
  color: white;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
}

.remove-ingredient {
  background: none;
  border: none;
  color: white;
  margin-left: 8px;
  cursor: pointer;
  font-size: 16px;
  font-weight: bold;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.3s;
}

.remove-ingredient:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.calorie-input {
  width: 200px;
  padding: 12px 16px;
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  font-size: 14px;
  transition: border-color 0.3s;
}

.calorie-input:focus {
  outline: none;
  border-color: #70e4c4;
  box-shadow: 0 0 0 3px rgba(112, 228, 196, 0.2);
}

/* Frequency buttons container */
.frequency-container {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.frequency-button {
  flex: 1;
  min-width: 60px;
  background: white;
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  padding: 12px 16px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;
  text-align: center;
}

.frequency-button:hover {
  border-color: #70e4c4;
  background-color: rgba(112, 228, 196, 0.05);
}

.frequency-button-selected {
  background: linear-gradient(to right, #70e4c4, #2bdfac);
  border-color: #70e4c4;
  color: white;
}

.frequency-button-selected:hover {
  background: linear-gradient(to right, #5dd4b8, #26c99a);
}

/* Summary section styles */
.summary-section {
  margin-top: 20px;
}

.summary-card {
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 20px;
  margin-top: 16px;
}

.summary-row {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
  gap: 16px;
}

.summary-row:last-child {
  margin-bottom: 0;
}

.summary-label {
  font-size: 14px;
  color: #78909c;
  font-weight: 500;
  flex: 1;
  min-width: 120px;
}

.summary-value {
  font-size: 14px;
  color: #2c3e50;
  font-weight: 600;
  flex: 2;
  text-align: right;
  word-break: break-word;
}

.save-section {
  text-align: center;
  padding-top: 20px;
  border-top: 1px solid #e9ecef;
}

.save-preferences-btn {
  background: linear-gradient(to right, #70e4c4, #2bdfac);
  color: white;
  border: none;
  padding: 14px 32px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;
  box-shadow: 0 4px 15px rgba(112, 228, 196, 0.3);
}

.save-preferences-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(112, 228, 196, 0.4);
}

.save-preferences-btn:disabled {
  background: linear-gradient(to right, #d1d1d1, #c8c8c8);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(112, 228, 196, 0.2);
  border-radius: 50%;
  border-top-color: #70e4c4;
  animation: spin 1s ease-in-out infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.success-message {
  background-color: #d4edda;
  color: #155724;
  padding: 12px 16px;
  border-radius: 8px;
  margin-bottom: 20px;
  border-left: 4px solid #28a745;
  display: flex;
  align-items: center;
}

.success-message::before {
  content: "✓";
  margin-right: 10px;
  font-size: 16px;
  font-weight: bold;
}

.error-message {
  background-color: #f8d7da;
  color: #721c24;
  padding: 12px 16px;
  border-radius: 8px;
  margin-bottom: 20px;
  border-left: 4px solid #dc3545;
  display: flex;
  align-items: center;
}

.error-message::before {
  content: "⚠";
  margin-right: 10px;
  font-size: 16px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .dietary-preferences-container {
    margin: 10px;
    padding: 16px;
  }
  
  .options-grid {
    grid-template-columns: 1fr;
  }
  
  .dietary-preferences-header h1 {
    font-size: 24px;
  }
  
  .calorie-input {
    width: 100%;
  }

  .frequency-container {
    flex-direction: column;
  }

  .frequency-button {
    flex: none;
    width: 100%;
  }
}

/* AI Conflict Detection Styles */
.conflict-warning-section {
  background-color: #fff3cd;
  border: 1px solid #ffc107;
  border-radius: 8px;
  padding: 20px;
  margin: 20px 0;
}

.conflict-header {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.warning-icon {
  font-size: 20px;
  margin-right: 10px;
}

.conflict-header h3 {
  color: #856404;
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.conflict-item {
  margin-bottom: 12px;
  padding: 10px;
  background-color: rgba(255, 193, 7, 0.1);
  border-radius: 6px;
}

.conflict-text {
  font-size: 14px;
  color: #333;
  line-height: 1.4;
}

.conflict-items {
  color: #dc3545;
  font-weight: 600;
}

.severity-badge {
  display: inline-block;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 600;
  margin-left: 8px;
}

.severity-badge.high {
  background-color: #dc3545;
  color: white;
}

.suggestions-container {
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px solid #ffc107;
}

.suggestions-container h4 {
  color: #856404;
  margin: 0 0 10px 0;
  font-size: 14px;
  font-weight: 600;
}

.suggestions-list {
  margin: 0;
  padding-left: 20px;
}

.suggestions-list li {
  color: #6c757d;
  font-size: 13px;
  margin-bottom: 5px;
  line-height: 1.4;
}

.conflict-note {
  margin-top: 15px;
  font-size: 12px;
  color: #6c757d;
  font-style: italic;
  margin-bottom: 0;
}

.checking-conflicts {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 8px;
  margin: 15px 0;
  color: #6c757d;
  font-size: 14px;
}

.checking-conflicts .spinner {
  width: 16px;
  height: 16px;
  border: 2px solid #e9ecef;
  border-top: 2px solid #20c5af;
  border-radius: 50%;
  animation: spin-conflicts 1s linear infinite;
  margin-right: 10px;
}

@keyframes spin-conflicts {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
