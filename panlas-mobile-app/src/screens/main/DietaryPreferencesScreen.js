import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { userAPI, aiAPI } from '../../services/api';
import { useAuth } from '../../context/AuthContext';
import { colors, legacyFonts as fonts, legacySpacing as spacing, borderRadius } from '../../styles/theme';
import { commonStyles } from '../../styles/commonStyles';

const DietaryPreferencesScreen = ({ navigation }) => {
  const [preferences, setPreferences] = useState({
    restrictions: [],
    allergies: [],
    dislikedIngredients: [],
    calorieTarget: '',
    mealFrequency: 3
  });
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [conflicts, setConflicts] = useState(null);
  const [checkingConflicts, setCheckingConflicts] = useState(false);
  const [familyMembers, setFamilyMembers] = useState([]);

  const { user } = useAuth();

  // Dietary restrictions options (matching database dietType fields)
  const dietaryOptions = [
    'Vegetarian',
    'Vegan',
    'Gluten-Free',
    'Dairy-Free',
    'Nut-Free',
    'Low-Carb',
    'Keto',
    'Pescatarian',
    'Halal'
  ];

  // Allergy options (matching database allergens field)
  const allergyOptions = [
    'Nuts',
    'Gluten',
    'Soy',
    'Dairy',
    'Eggs',
    'Fish',
    'Shellfish',
    'Sesame'
  ];

  useEffect(() => {
    loadPreferences();
    loadFamilyMembers();
  }, []);

  const loadFamilyMembers = async () => {
    try {
      const response = await userAPI.getFamilyMembers();
      setFamilyMembers(response.data || []);
    } catch (error) {
      console.error('Error loading family members:', error);
      setFamilyMembers([]);
    }
  };

  const loadPreferences = async () => {
    try {
      setLoading(true);
      const response = await userAPI.getDietaryPreferences();
      console.log('Loaded preferences:', response);
      
      if (response.success && response.dietaryPreferences) {
        setPreferences({
          restrictions: response.dietaryPreferences.restrictions || [],
          allergies: response.dietaryPreferences.allergies || [],
          dislikedIngredients: response.dietaryPreferences.dislikedIngredients || [],
          calorieTarget: response.dietaryPreferences.calorieTarget?.toString() || '',
          mealFrequency: response.dietaryPreferences.mealFrequency || 3
        });
      }
    } catch (error) {
      console.error('Error loading preferences:', error);
      Alert.alert('Error', 'Failed to load dietary preferences');
    } finally {
      setLoading(false);
    }
  };

  const savePreferences = async () => {
    try {
      setSaving(true);
      
      const preferencesToSave = {
        restrictions: preferences.restrictions,
        allergies: preferences.allergies,
        dislikedIngredients: preferences.dislikedIngredients,
        calorieTarget: preferences.calorieTarget ? parseInt(preferences.calorieTarget) : null,
        mealFrequency: preferences.mealFrequency
      };

      console.log('Saving preferences:', preferencesToSave);
      
      const response = await userAPI.updateDietaryPreferences(preferencesToSave);
      console.log('Save response:', response);
      
      if (response.success) {
        Alert.alert('Success', 'Dietary preferences saved successfully!');
        navigation.goBack();
      } else {
        Alert.alert('Error', 'Failed to save preferences');
      }
    } catch (error) {
      console.error('Error saving preferences:', error);
      Alert.alert('Error', 'Failed to save dietary preferences');
    } finally {
      setSaving(false);
    }
  };

  const toggleRestriction = (restriction) => {
    setPreferences(prev => {
      const newRestrictions = prev.restrictions.includes(restriction)
        ? prev.restrictions.filter(r => r !== restriction)
        : [...prev.restrictions, restriction];

      console.log('Toggling restriction:', restriction);
      console.log('Previous restrictions:', prev.restrictions);
      console.log('New restrictions:', newRestrictions);

      return {
        ...prev,
        restrictions: newRestrictions
      };
    });
  };

  const toggleAllergy = (allergy) => {
    setPreferences(prev => {
      const newAllergies = prev.allergies.includes(allergy)
        ? prev.allergies.filter(a => a !== allergy)
        : [...prev.allergies, allergy];

      console.log('Toggling allergy:', allergy);
      console.log('Previous allergies:', prev.allergies);
      console.log('New allergies:', newAllergies);

      return {
        ...prev,
        allergies: newAllergies
      };
    });
  };

  const checkDietaryConflicts = async (currentPreferences) => {
    try {
      setCheckingConflicts(true);
      console.log('Checking conflicts for preferences:', currentPreferences);
      console.log('Family members:', familyMembers);

      // Prepare family member preferences for conflict checking
      const familyPreferences = familyMembers.map(member => ({
        name: member.name,
        restrictions: member.dietaryPreferences?.restrictions || [],
        allergies: member.dietaryPreferences?.allergies || [],
        dislikedIngredients: member.dietaryPreferences?.dislikedIngredients || []
      }));

      const requestData = {
        userPreferences: {
          restrictions: currentPreferences.restrictions,
          allergies: currentPreferences.allergies,
          dislikedIngredients: currentPreferences.dislikedIngredients
        },
        familyMembers: familyPreferences
      };

      console.log('Sending conflict detection request:', requestData);

      const response = await aiAPI.detectDietaryConflicts(requestData);
      console.log('Conflict detection response:', response);

      if (response.data.success && response.data.conflicts) {
        console.log('Setting conflicts:', response.data.conflicts);
        setConflicts(response.data.conflicts);
      } else {
        console.log('No conflicts detected or response failed');
        setConflicts(null);
      }
    } catch (error) {
      console.error('Error checking dietary conflicts:', error);
      setConflicts(null);
    } finally {
      setCheckingConflicts(false);
    }
  };

  // Check for conflicts whenever preferences change
  useEffect(() => {
    if (preferences.restrictions.length > 0 || preferences.allergies.length > 0) {
      const timeoutId = setTimeout(() => {
        checkDietaryConflicts(preferences);
      }, 1000); // Debounce for 1 second

      return () => clearTimeout(timeoutId);
    } else {
      setConflicts(null);
    }
  }, [preferences.restrictions, preferences.allergies, preferences.dislikedIngredients, familyMembers]);

  const renderOptionButton = (option, isSelected, onPress, color = colors.primary) => (
    <TouchableOpacity
      key={option}
      style={[
        styles.optionButton,
        isSelected && { backgroundColor: color, borderColor: color }
      ]}
      onPress={onPress}
    >
      <Text style={[
        styles.optionText,
        isSelected && styles.optionTextSelected
      ]}>
        {option}
      </Text>
      {isSelected && (
        <Ionicons name="checkmark" size={16} color={colors.surface} style={styles.checkmark} />
      )}
    </TouchableOpacity>
  );

  if (loading) {
    return (
      <SafeAreaView style={commonStyles.container}>
        <View style={commonStyles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={commonStyles.loadingText}>Loading preferences...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={commonStyles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Ionicons name="arrow-back" size={24} color={colors.surface} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Dietary Preferences</Text>
        <TouchableOpacity onPress={savePreferences} disabled={saving}>
          {saving ? (
            <ActivityIndicator size="small" color={colors.surface} />
          ) : (
            <Text style={styles.saveButton}>Save</Text>
          )}
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.container}>
        {/* Dietary Restrictions Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Dietary Restrictions</Text>
          <Text style={styles.sectionSubtitle}>
            Select all that apply to you
          </Text>
          <View style={styles.optionsContainer}>
            {dietaryOptions.map(option => 
              renderOptionButton(
                option,
                preferences.restrictions.includes(option),
                () => toggleRestriction(option),
                colors.primary
              )
            )}
          </View>
        </View>

        {/* Allergies Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Allergies</Text>
          <Text style={styles.sectionSubtitle}>
            Select any food allergies you have
          </Text>
          <View style={styles.optionsContainer}>
            {allergyOptions.map(option =>
              renderOptionButton(
                option,
                preferences.allergies.includes(option),
                () => toggleAllergy(option),
                colors.secondary
              )
            )}
          </View>
        </View>

        {/* AI Conflict Detection Warning */}
        {conflicts && conflicts.hasConflicts && (
          <View style={styles.conflictSection}>
            <View style={styles.conflictHeader}>
              <Ionicons name="warning" size={20} color={colors.warning} />
              <Text style={styles.conflictTitle}>Dietary Conflicts Detected</Text>
            </View>
            {conflicts.conflicts.map((conflict, index) => (
              <View key={index} style={styles.conflictItem}>
                <Text style={styles.conflictText}>
                  <Text style={styles.conflictItems}>
                    {conflict.items.join(' + ')}
                  </Text>
                  {conflict.type === 'family' && (
                    <Text style={styles.conflictType}> (Family Conflict)</Text>
                  )}
                  {conflict.type === 'user-family' && (
                    <Text style={styles.conflictType}> (User vs Family)</Text>
                  )}
                  : {conflict.reason}
                </Text>
                {conflict.severity === 'high' && (
                  <View style={styles.severityBadge}>
                    <Text style={styles.severityText}>High</Text>
                  </View>
                )}
              </View>
            ))}
            {conflicts.suggestions && conflicts.suggestions.length > 0 && (
              <View style={styles.suggestionsContainer}>
                <Text style={styles.suggestionsTitle}>AI Suggestions:</Text>
                {conflicts.suggestions.map((suggestion, index) => (
                  <Text key={index} style={styles.suggestionText}>
                    • {suggestion}
                  </Text>
                ))}
              </View>
            )}
            <Text style={styles.conflictNote}>
              Note: You can still save these preferences, but consider the conflicts above.
            </Text>
          </View>
        )}

        {/* Loading indicator for conflict checking */}
        {checkingConflicts && (
          <View style={styles.checkingSection}>
            <ActivityIndicator size="small" color={colors.primary} />
            <Text style={styles.checkingText}>Checking for dietary conflicts...</Text>
          </View>
        )}

        {/* Test Conflict Button - TEMPORARY */}
        <View style={styles.section}>
          <TouchableOpacity
            style={[styles.frequencyButton, { backgroundColor: colors.secondary }]}
            onPress={() => {
              console.log('Testing conflict detection...');
              checkDietaryConflicts({
                restrictions: ['Keto', 'Vegan'],
                allergies: ['Nuts'],
                dislikedIngredients: []
              });
            }}
          >
            <Text style={[styles.frequencyText, { color: colors.surface }]}>
              Test Conflict Detection
            </Text>
          </TouchableOpacity>
        </View>

        {/* Calorie Target Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Daily Calorie Target</Text>
          <Text style={styles.sectionSubtitle}>
            Your target daily calorie intake (optional)
          </Text>
          <TextInput
            style={styles.textInput}
            placeholder="e.g., 2000"
            value={preferences.calorieTarget}
            onChangeText={(text) => setPreferences(prev => ({ ...prev, calorieTarget: text }))}
            keyboardType="numeric"
            placeholderTextColor={colors.textSecondary}
          />
        </View>

        {/* Meal Frequency Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Meal Frequency</Text>
          <Text style={styles.sectionSubtitle}>
            How many meals do you prefer per day?
          </Text>
          <View style={styles.frequencyContainer}>
            {[2, 3, 4, 5, 6].map(frequency => (
              <TouchableOpacity
                key={frequency}
                style={[
                  styles.frequencyButton,
                  preferences.mealFrequency === frequency && styles.frequencyButtonSelected
                ]}
                onPress={() => setPreferences(prev => ({ ...prev, mealFrequency: frequency }))}
              >
                <Text style={[
                  styles.frequencyText,
                  preferences.mealFrequency === frequency && styles.frequencyTextSelected
                ]}>
                  {frequency}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Summary Section */}
        <View style={styles.summarySection}>
          <Text style={styles.summaryTitle}>Summary</Text>
          <View style={styles.summaryCard}>
            <View style={styles.summaryRow}>
              <Text style={styles.summaryLabel}>Restrictions:</Text>
              <Text style={styles.summaryValue}>
                {preferences.restrictions.length > 0 ? preferences.restrictions.join(', ') : 'None'}
              </Text>
            </View>
            <View style={styles.summaryRow}>
              <Text style={styles.summaryLabel}>Allergies:</Text>
              <Text style={styles.summaryValue}>
                {preferences.allergies.length > 0 ? preferences.allergies.join(', ') : 'None'}
              </Text>
            </View>
            <View style={styles.summaryRow}>
              <Text style={styles.summaryLabel}>Daily Calories:</Text>
              <Text style={styles.summaryValue}>
                {preferences.calorieTarget || 'Not set'}
              </Text>
            </View>
            <View style={styles.summaryRow}>
              <Text style={styles.summaryLabel}>Meals per day:</Text>
              <Text style={styles.summaryValue}>
                {preferences.mealFrequency}
              </Text>
            </View>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    backgroundColor: colors.primary,
    paddingVertical: spacing.md,
    paddingHorizontal: spacing.md,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  headerTitle: {
    fontSize: fonts.sizes.large,
    fontWeight: 'bold',
    color: colors.surface,
  },
  saveButton: {
    fontSize: fonts.sizes.medium,
    color: colors.surface,
    fontWeight: '600',
  },
  section: {
    backgroundColor: colors.surface,
    marginTop: spacing.sm,
    padding: spacing.md,
  },
  sectionTitle: {
    fontSize: fonts.sizes.large,
    fontWeight: 'bold',
    color: colors.text,
    marginBottom: spacing.xs,
  },
  sectionSubtitle: {
    fontSize: fonts.sizes.medium,
    color: colors.textSecondary,
    marginBottom: spacing.md,
  },
  optionsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: spacing.sm,
  },
  optionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.background,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderRadius: borderRadius.medium,
    borderWidth: 1,
    borderColor: colors.border,
    marginBottom: spacing.sm,
  },
  optionText: {
    fontSize: fonts.sizes.medium,
    color: colors.text,
    fontWeight: '500',
  },
  optionTextSelected: {
    color: colors.surface,
  },
  checkmark: {
    marginLeft: spacing.xs,
  },
  textInput: {
    backgroundColor: colors.background,
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: borderRadius.medium,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.md,
    fontSize: fonts.sizes.medium,
    color: colors.text,
  },
  frequencyContainer: {
    flexDirection: 'row',
    gap: spacing.sm,
  },
  frequencyButton: {
    flex: 1,
    backgroundColor: colors.background,
    paddingVertical: spacing.md,
    borderRadius: borderRadius.medium,
    borderWidth: 1,
    borderColor: colors.border,
    alignItems: 'center',
  },
  frequencyButtonSelected: {
    backgroundColor: colors.primary,
    borderColor: colors.primary,
  },
  frequencyText: {
    fontSize: fonts.sizes.medium,
    color: colors.text,
    fontWeight: '600',
  },
  frequencyTextSelected: {
    color: colors.surface,
  },
  summarySection: {
    backgroundColor: colors.surface,
    marginTop: spacing.sm,
    padding: spacing.md,
    marginBottom: spacing.xl,
  },
  summaryTitle: {
    fontSize: fonts.sizes.large,
    fontWeight: 'bold',
    color: colors.text,
    marginBottom: spacing.md,
  },
  summaryCard: {
    backgroundColor: colors.background,
    borderRadius: borderRadius.medium,
    padding: spacing.md,
    borderWidth: 1,
    borderColor: colors.border,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: spacing.sm,
  },
  summaryLabel: {
    fontSize: fonts.sizes.medium,
    color: colors.textSecondary,
    fontWeight: '500',
    flex: 1,
  },
  summaryValue: {
    fontSize: fonts.sizes.medium,
    color: colors.text,
    fontWeight: '600',
    flex: 2,
    textAlign: 'right',
  },
  // Conflict detection styles
  conflictSection: {
    backgroundColor: '#FFF3CD',
    borderColor: colors.warning,
    borderWidth: 1,
    borderRadius: borderRadius.medium,
    margin: spacing.md,
    padding: spacing.md,
  },
  conflictHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  conflictTitle: {
    fontSize: fonts.sizes.medium,
    fontWeight: 'bold',
    color: colors.warning,
    marginLeft: spacing.xs,
  },
  conflictItem: {
    marginBottom: spacing.sm,
    flexDirection: 'row',
    alignItems: 'flex-start',
    justifyContent: 'space-between',
  },
  conflictText: {
    fontSize: fonts.sizes.small,
    color: colors.text,
    flex: 1,
  },
  conflictItems: {
    fontWeight: 'bold',
    color: colors.warning,
  },
  conflictType: {
    fontWeight: 'bold',
    color: colors.secondary,
    fontSize: fonts.sizes.small,
  },
  severityBadge: {
    backgroundColor: colors.error,
    paddingHorizontal: spacing.xs,
    paddingVertical: 2,
    borderRadius: borderRadius.small,
    marginLeft: spacing.xs,
  },
  severityText: {
    fontSize: fonts.sizes.small,
    color: colors.surface,
    fontWeight: 'bold',
  },
  suggestionsContainer: {
    marginTop: spacing.sm,
    paddingTop: spacing.sm,
    borderTopWidth: 1,
    borderTopColor: colors.border,
  },
  suggestionsTitle: {
    fontSize: fonts.sizes.small,
    fontWeight: 'bold',
    color: colors.text,
    marginBottom: spacing.xs,
  },
  suggestionText: {
    fontSize: fonts.sizes.small,
    color: colors.textSecondary,
    marginBottom: spacing.xs,
  },
  conflictNote: {
    fontSize: fonts.sizes.small,
    color: colors.textSecondary,
    fontStyle: 'italic',
    marginTop: spacing.sm,
  },
  checkingSection: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.surface,
    margin: spacing.md,
    padding: spacing.md,
    borderRadius: borderRadius.medium,
  },
  checkingText: {
    fontSize: fonts.sizes.small,
    color: colors.textSecondary,
    marginLeft: spacing.sm,
  },
});

export default DietaryPreferencesScreen;
