import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  TouchableOpacity,
  FlatList,
  Modal,
  TextInput,
  Alert,
  RefreshControl,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

import { userAPI } from '../../services/api';
import { useAuth } from '../../context/AuthContext';
import { colors, legacyFonts as fonts, legacySpacing as spacing, borderRadius } from '../../styles/theme';
import { commonStyles } from '../../styles/commonStyles';

const FamilyScreen = ({ navigation }) => {
  const [familyMembers, setFamilyMembers] = useState([]);
  const [userDietaryPreferences, setUserDietaryPreferences] = useState({});
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [showAddMemberModal, setShowAddMemberModal] = useState(false);
  const [showEditPreferencesModal, setShowEditPreferencesModal] = useState(false);
  const [editingMember, setEditingMember] = useState(null);
  const [newMember, setNewMember] = useState({
    name: '',
    dateOfBirth: '',
    dietaryPreferences: {
      restrictions: [],
      allergies: [],
      dislikedIngredients: [],
      calorieTarget: '',
      mealFrequency: 3
    }
  });

  const { user } = useAuth();

  // Helper functions for multi-select
  const toggleNewMemberRestriction = (restriction) => {
    setNewMember(prev => ({
      ...prev,
      dietaryPreferences: {
        ...prev.dietaryPreferences,
        restrictions: prev.dietaryPreferences.restrictions.includes(restriction)
          ? prev.dietaryPreferences.restrictions.filter(r => r !== restriction)
          : [...prev.dietaryPreferences.restrictions, restriction]
      }
    }));
  };

  const toggleNewMemberAllergy = (allergy) => {
    setNewMember(prev => ({
      ...prev,
      dietaryPreferences: {
        ...prev.dietaryPreferences,
        allergies: prev.dietaryPreferences.allergies.includes(allergy)
          ? prev.dietaryPreferences.allergies.filter(a => a !== allergy)
          : [...prev.dietaryPreferences.allergies, allergy]
      }
    }));
  };

  const toggleUserRestriction = (restriction) => {
    setUserDietaryPreferences(prev => ({
      ...prev,
      restrictions: (prev.restrictions || []).includes(restriction)
        ? (prev.restrictions || []).filter(r => r !== restriction)
        : [...(prev.restrictions || []), restriction]
    }));
  };

  const toggleUserAllergy = (allergy) => {
    setUserDietaryPreferences(prev => ({
      ...prev,
      allergies: (prev.allergies || []).includes(allergy)
        ? (prev.allergies || []).filter(a => a !== allergy)
        : [...(prev.allergies || []), allergy]
    }));
  };

  const renderOptionButton = (option, isSelected, onPress, color = colors.primary) => (
    <TouchableOpacity
      key={option}
      style={[
        styles.optionButton,
        isSelected && { backgroundColor: color, borderColor: color }
      ]}
      onPress={onPress}
    >
      <Text style={[
        styles.optionText,
        isSelected && styles.optionTextSelected
      ]}>
        {option}
      </Text>
      {isSelected && (
        <Ionicons name="checkmark" size={16} color={colors.surface} style={styles.checkmark} />
      )}
    </TouchableOpacity>
  );

  // Calculate age from date of birth
  const calculateAge = (dateOfBirth) => {
    if (!dateOfBirth) return null;

    const today = new Date();
    const birthDate = new Date(dateOfBirth);

    if (isNaN(birthDate.getTime())) return null;

    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();

    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }

    return age;
  };

  const dietaryOptions = [
    'Vegetarian',
    'Vegan',
    'Gluten-Free',
    'Dairy-Free',
    'Nut-Free',
    'Low-Carb',
    'Keto',
    'Pescatarian',
    'Halal'
  ];

  const allergyOptions = [
    'Nuts',
    'Gluten',
    'Soy',
    'Dairy',
    'Eggs',
    'Fish',
    'Shellfish',
    'Sesame'
  ];

  useEffect(() => {
    loadFamilyData();
  }, []);

  const loadFamilyData = async () => {
    try {
      setLoading(true);
      await Promise.all([
        loadFamilyMembers(),
        loadUserDietaryPreferences(),
      ]);
    } catch (error) {
      console.error('Error loading family data:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadFamilyMembers = async () => {
    try {
      const response = await userAPI.getFamilyMembers();
      setFamilyMembers(response.data || []);
    } catch (error) {
      console.error('Error loading family members:', error);
    }
  };

  const loadUserDietaryPreferences = async () => {
    try {
      const response = await userAPI.getDietaryPreferences();
      // Handle nested dietaryPreferences structure from backend
      const preferences = response.data?.dietaryPreferences || response.data || {};
      setUserDietaryPreferences(preferences);
    } catch (error) {
      console.error('Error loading dietary preferences:', error);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadFamilyData();
    setRefreshing(false);
  };

  const handleAddMember = async () => {
    try {
      if (!newMember.name.trim()) {
        Alert.alert('Error', 'Please enter a name for the family member');
        return;
      }

      await userAPI.addFamilyMember(newMember);
      setShowAddMemberModal(false);
      setNewMember({
        name: '',
        dateOfBirth: '',
        dietaryPreferences: {
          restrictions: [],
          allergies: [],
          dislikedIngredients: [],
          calorieTarget: '',
          mealFrequency: 3
        }
      });
      await loadFamilyMembers();
      Alert.alert('Success', 'Family member added successfully');
    } catch (error) {
      console.error('Error adding family member:', error);
      Alert.alert('Error', 'Failed to add family member');
    }
  };

  const handleRemoveMember = (memberId) => {
    Alert.alert(
      'Remove Family Member',
      'Are you sure you want to remove this family member?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Remove',
          style: 'destructive',
          onPress: async () => {
            try {
              await userAPI.removeFamilyMember(memberId);
              await loadFamilyMembers();
              Alert.alert('Success', 'Family member removed successfully');
            } catch (error) {
              console.error('Error removing family member:', error);
              Alert.alert('Error', 'Failed to remove family member');
            }
          }
        }
      ]
    );
  };

  const handleUpdateUserPreferences = async () => {
    try {
      await userAPI.updateDietaryPreferences(userDietaryPreferences);
      setShowEditPreferencesModal(false);
      Alert.alert('Success', 'Dietary preferences updated successfully');
    } catch (error) {
      console.error('Error updating dietary preferences:', error);
      Alert.alert('Error', 'Failed to update dietary preferences');
    }
  };

  const renderFamilyMember = ({ item: member }) => (
    <View style={styles.memberCard}>
      <View style={styles.memberHeader}>
        <View style={styles.memberAvatar}>
          <Text style={styles.memberAvatarText}>
            {member.name.charAt(0).toUpperCase()}
          </Text>
        </View>
        <View style={styles.memberInfo}>
          <Text style={styles.memberName}>{member.name}</Text>
          {member.dateOfBirth && (
            <Text style={styles.memberAge}>
              Age: {calculateAge(member.dateOfBirth)} years old
            </Text>
          )}
          <Text style={styles.memberPreferences}>
            {member.dietaryPreferences?.restrictions?.length > 0
              ? member.dietaryPreferences.restrictions.join(', ')
              : 'No dietary restrictions'
            }
          </Text>
        </View>
        <TouchableOpacity
          style={styles.removeButton}
          onPress={() => handleRemoveMember(member._id)}
        >
          <Ionicons name="close-circle" size={24} color={colors.secondary} />
        </TouchableOpacity>
      </View>

      {member.dietaryPreferences?.allergies?.length > 0 && (
        <View style={styles.allergiesSection}>
          <Text style={styles.allergiesLabel}>Allergies:</Text>
          <Text style={styles.allergiesText}>
            {member.dietaryPreferences.allergies.join(', ')}
          </Text>
        </View>
      )}
    </View>
  );

  return (
    <SafeAreaView style={commonStyles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Ionicons name="arrow-back" size={24} color={colors.surface} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Family Profile</Text>
        <TouchableOpacity
          style={styles.addButton}
          onPress={() => setShowAddMemberModal(true)}
        >
          <Ionicons name="add" size={24} color={colors.surface} />
        </TouchableOpacity>
      </View>

      <ScrollView
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[colors.primary]}
          />
        }
      >
        {/* Family Members Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Family Members</Text>
          {familyMembers.length > 0 ? (
            <FlatList
              data={familyMembers}
              renderItem={renderFamilyMember}
              keyExtractor={(item) => item._id || item.id}
              scrollEnabled={false}
            />
          ) : (
            <View style={styles.emptyContainer}>
              <Ionicons name="people-outline" size={48} color={colors.textSecondary} />
              <Text style={styles.emptyText}>No family members added</Text>
              <Text style={styles.emptySubtext}>
                Add family members to customize meal plans for everyone
              </Text>
            </View>
          )}
        </View>

        {/* User Dietary Preferences Section */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Your Dietary Preferences</Text>
            <TouchableOpacity
              style={styles.editButton}
              onPress={() => setShowEditPreferencesModal(true)}
            >
              <Ionicons name="pencil" size={20} color={colors.primary} />
              <Text style={styles.editButtonText}>Edit</Text>
            </TouchableOpacity>
          </View>

          <View style={styles.preferencesCard}>
            <View style={styles.preferenceRow}>
              <Text style={styles.preferenceLabel}>Restrictions:</Text>
              <Text style={styles.preferenceValue}>
                {userDietaryPreferences.restrictions?.length > 0
                  ? userDietaryPreferences.restrictions.join(', ')
                  : 'None'
                }
              </Text>
            </View>

            <View style={styles.preferenceRow}>
              <Text style={styles.preferenceLabel}>Allergies:</Text>
              <Text style={styles.preferenceValue}>
                {userDietaryPreferences.allergies?.length > 0
                  ? userDietaryPreferences.allergies.join(', ')
                  : 'None'
                }
              </Text>
            </View>

            {userDietaryPreferences.calorieTarget && (
              <View style={styles.preferenceRow}>
                <Text style={styles.preferenceLabel}>Daily Calorie Target:</Text>
                <Text style={styles.preferenceValue}>
                  {userDietaryPreferences.calorieTarget} calories
                </Text>
              </View>
            )}
          </View>
        </View>
      </ScrollView>

      {/* Add Member Modal */}
      <Modal
        visible={showAddMemberModal}
        animationType="slide"
        presentationStyle="pageSheet"
      >
        <SafeAreaView style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <TouchableOpacity onPress={() => setShowAddMemberModal(false)}>
              <Text style={styles.modalCancelText}>Cancel</Text>
            </TouchableOpacity>
            <Text style={styles.modalTitle}>Add Family Member</Text>
            <TouchableOpacity onPress={handleAddMember}>
              <Text style={styles.modalSaveText}>Save</Text>
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.modalContent}>
            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Name</Text>
              <TextInput
                style={styles.textInput}
                value={newMember.name}
                onChangeText={(text) => setNewMember(prev => ({ ...prev, name: text }))}
                placeholder="Enter family member's name"
                placeholderTextColor={colors.textSecondary}
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Date of Birth (Optional)</Text>
              <TextInput
                style={styles.textInput}
                value={newMember.dateOfBirth}
                onChangeText={(text) => setNewMember(prev => ({ ...prev, dateOfBirth: text }))}
                placeholder="YYYY-MM-DD (e.g., 1990-05-15)"
                placeholderTextColor={colors.textSecondary}
              />
              <Text style={styles.inputHint}>
                Enter date in YYYY-MM-DD format to calculate age
              </Text>
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Dietary Restrictions</Text>
              <Text style={styles.inputSubtitle}>Select all that apply</Text>
              <View style={styles.optionsContainer}>
                {dietaryOptions.map(option =>
                  renderOptionButton(
                    option,
                    newMember.dietaryPreferences.restrictions.includes(option),
                    () => toggleNewMemberRestriction(option),
                    colors.primary
                  )
                )}
              </View>
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Allergies</Text>
              <Text style={styles.inputSubtitle}>Select any food allergies</Text>
              <View style={styles.optionsContainer}>
                {allergyOptions.map(option =>
                  renderOptionButton(
                    option,
                    newMember.dietaryPreferences.allergies.includes(option),
                    () => toggleNewMemberAllergy(option),
                    colors.secondary
                  )
                )}
              </View>
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Daily Calorie Target</Text>
              <TextInput
                style={styles.textInput}
                value={newMember.dietaryPreferences.calorieTarget}
                onChangeText={(text) => setNewMember(prev => ({
                  ...prev,
                  dietaryPreferences: {
                    ...prev.dietaryPreferences,
                    calorieTarget: text
                  }
                }))}
                placeholder="e.g., 2000"
                placeholderTextColor={colors.textSecondary}
                keyboardType="numeric"
              />
            </View>
          </ScrollView>
        </SafeAreaView>
      </Modal>

      {/* Edit User Preferences Modal */}
      <Modal
        visible={showEditPreferencesModal}
        animationType="slide"
        presentationStyle="pageSheet"
      >
        <SafeAreaView style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <TouchableOpacity onPress={() => setShowEditPreferencesModal(false)}>
              <Text style={styles.modalCancelText}>Cancel</Text>
            </TouchableOpacity>
            <Text style={styles.modalTitle}>Edit Your Preferences</Text>
            <TouchableOpacity onPress={handleUpdateUserPreferences}>
              <Text style={styles.modalSaveText}>Save</Text>
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.modalContent}>
            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Dietary Restrictions</Text>
              <Text style={styles.inputSubtitle}>Select all that apply to you</Text>
              <View style={styles.optionsContainer}>
                {dietaryOptions.map(option =>
                  renderOptionButton(
                    option,
                    (userDietaryPreferences.restrictions || []).includes(option),
                    () => toggleUserRestriction(option),
                    colors.primary
                  )
                )}
              </View>
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Allergies</Text>
              <Text style={styles.inputSubtitle}>Select any food allergies you have</Text>
              <View style={styles.optionsContainer}>
                {allergyOptions.map(option =>
                  renderOptionButton(
                    option,
                    (userDietaryPreferences.allergies || []).includes(option),
                    () => toggleUserAllergy(option),
                    colors.secondary
                  )
                )}
              </View>
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Daily Calorie Target</Text>
              <TextInput
                style={styles.textInput}
                value={userDietaryPreferences.calorieTarget?.toString() || ''}
                onChangeText={(text) => setUserDietaryPreferences(prev => ({
                  ...prev,
                  calorieTarget: text
                }))}
                placeholder="e.g., 2000"
                placeholderTextColor={colors.textSecondary}
                keyboardType="numeric"
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Meal Frequency</Text>
              <Text style={styles.inputSubtitle}>How many meals do you prefer per day?</Text>
              <View style={styles.frequencyContainer}>
                {[2, 3, 4, 5, 6].map(frequency => (
                  <TouchableOpacity
                    key={frequency}
                    style={[
                      styles.frequencyButton,
                      userDietaryPreferences.mealFrequency === frequency && styles.frequencyButtonSelected
                    ]}
                    onPress={() => setUserDietaryPreferences(prev => ({ ...prev, mealFrequency: frequency }))}
                  >
                    <Text style={[
                      styles.frequencyText,
                      userDietaryPreferences.mealFrequency === frequency && styles.frequencyTextSelected
                    ]}>
                      {frequency}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>
          </ScrollView>
        </SafeAreaView>
      </Modal>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  header: {
    backgroundColor: colors.primary,
    paddingVertical: spacing.md,
    paddingHorizontal: spacing.md,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  headerTitle: {
    fontSize: fonts.sizes.large,
    fontWeight: 'bold',
    color: colors.surface,
  },
  addButton: {
    padding: spacing.sm,
  },
  section: {
    backgroundColor: colors.surface,
    marginBottom: spacing.md,
    padding: spacing.md,
  },
  sectionTitle: {
    fontSize: fonts.sizes.large,
    fontWeight: 'bold',
    color: colors.text,
    marginBottom: spacing.md,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  editButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.background,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderRadius: borderRadius.medium,
    borderWidth: 1,
    borderColor: colors.primary,
  },
  editButtonText: {
    fontSize: fonts.sizes.small,
    color: colors.primary,
    marginLeft: spacing.xs,
    fontWeight: '500',
  },
  memberCard: {
    backgroundColor: colors.background,
    borderRadius: borderRadius.medium,
    padding: spacing.md,
    marginBottom: spacing.md,
    ...commonStyles.shadowSmall,
  },
  memberHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  memberAvatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: colors.primary,
    alignItems: 'center',
    justifyContent: 'center',
  },
  memberAvatarText: {
    fontSize: fonts.sizes.large,
    fontWeight: 'bold',
    color: colors.surface,
  },
  memberInfo: {
    flex: 1,
    marginLeft: spacing.md,
  },
  memberName: {
    fontSize: fonts.sizes.medium,
    fontWeight: 'bold',
    color: colors.text,
  },
  memberAge: {
    fontSize: fonts.sizes.small,
    color: colors.textSecondary,
    marginTop: spacing.xs,
  },
  memberPreferences: {
    fontSize: fonts.sizes.small,
    color: colors.textSecondary,
    marginTop: spacing.xs,
  },
  removeButton: {
    padding: spacing.sm,
  },
  allergiesSection: {
    marginTop: spacing.md,
    paddingTop: spacing.md,
    borderTopWidth: 1,
    borderTopColor: colors.border,
  },
  allergiesLabel: {
    fontSize: fonts.sizes.small,
    fontWeight: 'bold',
    color: colors.text,
  },
  allergiesText: {
    fontSize: fonts.sizes.small,
    color: colors.textSecondary,
    marginTop: spacing.xs,
  },
  preferencesCard: {
    backgroundColor: colors.background,
    borderRadius: borderRadius.medium,
    padding: spacing.md,
  },
  preferenceRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: spacing.sm,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  preferenceLabel: {
    fontSize: fonts.sizes.medium,
    fontWeight: '500',
    color: colors.text,
    flex: 1,
  },
  preferenceValue: {
    fontSize: fonts.sizes.medium,
    color: colors.textSecondary,
    flex: 2,
    textAlign: 'right',
  },
  emptyContainer: {
    alignItems: 'center',
    paddingVertical: spacing.xl,
  },
  emptyText: {
    fontSize: fonts.sizes.medium,
    fontWeight: '500',
    color: colors.textSecondary,
    marginTop: spacing.md,
  },
  emptySubtext: {
    fontSize: fonts.sizes.small,
    color: colors.textSecondary,
    marginTop: spacing.xs,
    textAlign: 'center',
  },
  modalContainer: {
    flex: 1,
    backgroundColor: colors.background,
  },
  modalHeader: {
    backgroundColor: colors.surface,
    paddingVertical: spacing.md,
    paddingHorizontal: spacing.md,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  modalTitle: {
    fontSize: fonts.sizes.large,
    fontWeight: 'bold',
    color: colors.text,
  },
  modalCancelText: {
    fontSize: fonts.sizes.medium,
    color: colors.textSecondary,
  },
  modalSaveText: {
    fontSize: fonts.sizes.medium,
    color: colors.primary,
    fontWeight: 'bold',
  },
  modalContent: {
    flex: 1,
    padding: spacing.md,
  },
  inputGroup: {
    marginBottom: spacing.lg,
  },
  inputLabel: {
    fontSize: fonts.sizes.medium,
    fontWeight: '500',
    color: colors.text,
    marginBottom: spacing.sm,
  },
  inputSubtitle: {
    fontSize: fonts.sizes.small,
    color: colors.textSecondary,
    marginBottom: spacing.md,
  },
  optionsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: spacing.sm,
  },
  optionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.background,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderRadius: borderRadius.medium,
    borderWidth: 1,
    borderColor: colors.border,
    marginBottom: spacing.sm,
  },
  optionText: {
    fontSize: fonts.sizes.medium,
    color: colors.text,
    fontWeight: '500',
  },
  optionTextSelected: {
    color: colors.surface,
  },
  checkmark: {
    marginLeft: spacing.xs,
  },
  frequencyContainer: {
    flexDirection: 'row',
    gap: spacing.sm,
  },
  frequencyButton: {
    flex: 1,
    backgroundColor: colors.background,
    paddingVertical: spacing.md,
    borderRadius: borderRadius.medium,
    borderWidth: 1,
    borderColor: colors.border,
    alignItems: 'center',
  },
  frequencyButtonSelected: {
    backgroundColor: colors.primary,
    borderColor: colors.primary,
  },
  frequencyText: {
    fontSize: fonts.sizes.medium,
    color: colors.text,
    fontWeight: '600',
  },
  frequencyTextSelected: {
    color: colors.surface,
  },
  inputHint: {
    fontSize: fonts.sizes.small,
    color: colors.textSecondary,
    marginTop: spacing.xs,
    fontStyle: 'italic',
  },
  textInput: {
    backgroundColor: colors.surface,
    borderRadius: borderRadius.medium,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.md,
    fontSize: fonts.sizes.medium,
    color: colors.text,
    borderWidth: 1,
    borderColor: colors.border,
  },
  pickerContainer: {
    backgroundColor: colors.surface,
    borderRadius: borderRadius.medium,
    borderWidth: 1,
    borderColor: colors.border,
  },
  picker: {
    height: 50,
  },
});

export default FamilyScreen;
